<?php
$page_title = "إضافة عميل جديد"; // تحديد عنوان الصفحة
require_once dirname(__DIR__) . '/config.php';
$class1 = "active";
require_once BASE_DIR . 'partials/session_user.php';
require_once BASE_DIR . 'partials/header.php';
require_once BASE_DIR . 'partials/navbar.php';

// تعريف المتغيرات
$name = $mobile = $city = $address = "";
$name_err = $mobile_err = $city_err = "";
$message = "";

// جلب توكن CSRF
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// --- معالجة النموذج عند الإرسال ---
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_customer'])) {

    // التحقق من CSRF
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $message = "<div class='alert alert-danger'>خطأ: طلب غير صالح (CSRF detected).</div>";
    } else {
        // التحقق من الاسم
        if (empty(trim($_POST["name"]))) { $name_err = "الرجاء إدخال اسم العميل."; }
        else { $name = trim($_POST["name"]); }

        // التحقق من المدينة
        if (empty(trim($_POST["city"]))) { $city_err = "الرجاء إدخال المدينة."; }
        else { $city = trim($_POST["city"]); }

        // التحقق من الموبايل
        if (empty(trim($_POST["mobile"]))) { $mobile_err = "الرجاء إدخال رقم الموبايل."; }
        elseif (!preg_match('/^[0-9]{11}$/', trim($_POST["mobile"]))) { $mobile_err = "يجب أن يتكون رقم الموبايل من 11 رقمًا بالضبط."; }
        else {
            $mobile_check_val = trim($_POST["mobile"]);
            $sql_check_mobile = "SELECT id FROM customers WHERE mobile = ?";
            if ($stmt_check = $conn->prepare($sql_check_mobile)) {
                $stmt_check->bind_param("s", $mobile_check_val);
                if ($stmt_check->execute()) {
                    $stmt_check->store_result();
                    if ($stmt_check->num_rows > 0) { $mobile_err = "رقم الموبايل هذا مسجل بالفعل."; }
                    else { $mobile = $mobile_check_val; }
                } else { $message = "<div class='alert alert-danger'>حدث خطأ ما. الرجاء المحاولة مرة أخرى.</div>"; }
                $stmt_check->close();
            }
        }

        // جلب العنوان (اختياري)
        $address = trim($_POST["address"]);

        // التحقق من عدم وجود أخطاء قبل الإدراج
        if (empty($name_err) && empty($mobile_err) && empty($city_err) && empty($message)) {
            $sql_insert = "INSERT INTO customers (name, mobile, city, address, created_by) VALUES (?, ?, ?, ?, ?)";
            if ($stmt_insert = $conn->prepare($sql_insert)) {
                $stmt_insert->bind_param("ssssi", $name, $mobile, $city, $address, $created_by);
                $created_by = $_SESSION["id"];

                if ($stmt_insert->execute()) {
                    // --- !! تغيير PRG هنا !! ---
                    // تخزين رسالة النجاح في الجلسة
                    $_SESSION['message'] = "<div class='alert alert-success'>تم إضافة العميل بنجاح!</div>";
                    // إعادة التوجيه إلى صفحة عرض العملاء
                    header("Location: " . BASE_URL . "customer/show.php");
                    exit; // إيقاف التنفيذ بعد إعادة التوجيه
                    // --- !! نهاية تغيير PRG !! ---
                } else {
                    $message = "<div class='alert alert-danger'>حدث خطأ أثناء إضافة العميل: " . $stmt_insert->error . "</div>";
                }
                $stmt_insert->close();
            } else {
                 $message = "<div class='alert alert-danger'>خطأ في تحضير الاستعلام: " . $conn->error . "</div>";
            }
        } else {
             if (empty($message)) {
                $message = "<div class='alert alert-danger'>الرجاء إصلاح الأخطاء في النموذج.</div>";
             }
        }
    }
}

?>

<div class="container mt-5 pt-3">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white text-center">
                    <h2><i class="fas fa-user-plus"></i> إضافة عميل جديد</h2>
                </div>
                <div class="card-body p-4">

                    <?php echo $message; // عرض رسائل الحالة (ستظهر الآن فقط عند حدوث خطأ) ?>

                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                        <div class="mb-3">
                            <label for="name" class="form-label"><i class="fas fa-user"></i> اسم العميل:</label>
                            <input type="text" name="name" id="name" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($name); ?>">
                            <span class="invalid-feedback"><?php echo $name_err; ?></span>
                        </div>

                        <div class="mb-3">
                            <label for="mobile" class="form-label"><i class="fas fa-mobile-alt"></i> رقم الموبايل (11 رقم):</label>
                            <input type="tel" name="mobile" id="mobile" class="form-control <?php echo (!empty($mobile_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($mobile); ?>" pattern="[0-9]{11}" title="يجب إدخال 11 رقماً">
                            <span class="invalid-feedback"><?php echo $mobile_err; ?></span>
                        </div>

                        <div class="mb-3">
                            <label for="city" class="form-label"><i class="fas fa-city"></i> المدينة:</label>
                            <input type="text" name="city" id="city" class="form-control <?php echo (!empty($city_err)) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($city); ?>">
                            <span class="invalid-feedback"><?php echo $city_err; ?></span>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label"><i class="fas fa-map-marker-alt"></i> العنوان (اختياري):</label>
                            <textarea name="address" id="address" class="form-control" rows="3"><?php echo htmlspecialchars($address); ?></textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="add_customer" class="btn btn-primary btn-lg"><i class="fas fa-plus-circle"></i> إضافة العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $conn->close();?>
<?php require_once BASE_DIR . 'partials/footer.php'; ?>